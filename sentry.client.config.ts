// This file configures the initialization of Sentry on the client.
// The config you add here will be used whenever a users loads a page in their browser.
// https://docs.sentry.io/platforms/javascript/guides/nextjs/

import * as Sentry from '@sentry/nextjs';

// Only initialize Sentry in production environment
if (process.env.NODE_ENV === 'production') {
  Sentry.init({
    dsn: process.env.NEXT_PUBLIC_SENTRY_DSN,

    // Add optional integrations for additional features
    integrations: [Sentry.replayIntegration()],

    // Adjust sample rate for production - 10% for performance monitoring
    tracesSampleRate: 0.1,

    // Define how likely Replay events are sampled.
    // 1% for regular sessions to reduce quota usage
    replaysSessionSampleRate: 0.01,

    // 100% replay when an error occurs - this is critical for debugging
    replaysOnErrorSampleRate: 1.0,

    // Disable debug in production
    debug: false,

    // Set environment for better error tracking
    environment: process.env.NODE_ENV,

    // Enable automatic instrumentation
    beforeSend(event) {
      // Filter out noisy errors that aren't actionable
      if (event.exception) {
        const error = event.exception.values?.[0];
        if (error?.value?.includes('ResizeObserver loop limit exceeded')) {
          return null;
        }
        if (error?.value?.includes('Script error.')) {
          return null;
        }
        if (error?.value?.includes('Non-Error promise rejection captured')) {
          return null;
        }
      }
      return event;
    },
  });
}
