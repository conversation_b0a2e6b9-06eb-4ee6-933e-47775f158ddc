import * as Sentry from '@sentry/nextjs';

export async function register() {
  if (process.env.NEXT_RUNTIME === 'nodejs') {
    // Initialize error monitoring services for Node.js runtime
    try {
      await import('../sentry.server.config');
      console.log('Sentry server configuration loaded');
    } catch (error) {
      console.error('Failed to load Sentry server configuration:', error);
    }

    try {
      await import('pino');
    } catch (error) {
      console.error('Failed to load pino logger:', error);
    }

    // Initialize OpenTelemetry only if <PERSON><PERSON> is configured
    if (process.env.HONEYCOMB_API_KEY) {
      try {
        const opentelemetry = await import('@opentelemetry/sdk-node');
        const { OTLPTraceExporter } = await import(
          '@opentelemetry/exporter-trace-otlp-http'
        );
        const { getNodeAutoInstrumentations } = await import(
          '@opentelemetry/auto-instrumentations-node'
        );

        const sdk = new opentelemetry.NodeSDK({
          serviceName: 'sabi-chat-backend',
          traceExporter: new OTLPTraceExporter({
            url:
              process.env.HONEYCOMB_API_ENDPOINT ||
              'https://api.honeycomb.io/v1/traces',
            headers: {
              'x-honeycomb-team': process.env.HONEYCOMB_API_KEY,
            },
          }),
          instrumentations: [
            getNodeAutoInstrumentations({
              '@opentelemetry/instrumentation-fs': {
                enabled: false,
              },
            }),
          ],
        });

        sdk.start();
        console.log('OpenTelemetry SDK started');
      } catch (error) {
        console.error('Failed to initialize OpenTelemetry:', error);
      }
    }
  }

  if (process.env.NEXT_RUNTIME === 'edge') {
    // Initialize error monitoring services for edge runtime
    // Note: Only Sentry is compatible with edge runtime
    try {
      await import('../sentry.edge.config');
      console.log('Sentry edge configuration loaded');
    } catch (error) {
      console.error('Failed to load Sentry edge configuration:', error);
    }
  }
}

export const onRequestError = Sentry.captureRequestError;
