import Stripe from 'stripe';
import { SubscriptionPlan } from './supabase/types';
import { logger } from './logger';

const log = logger.child({
  module: 'Stripe',
});

const STRIPE_API_KEY = process.env.STRIPE_SECRET_KEY || '';
const STRIPE_WEBHOOK_SECRET = process.env.STRIPE_WEBHOOK_SECRET || '';

// Stripe price IDs for each plan
export const STRIPE_PRICES: Record<Exclude<SubscriptionPlan, 'free'>, string> = {
  starter: process.env.STRIPE_PRICE_STARTER_MONTHLY || '',
  premium: process.env.STRIPE_PRICE_PREMIUM_MONTHLY || '',
};

// New pricing structure with monthly and yearly options
export const STRIPE_PRICE_IDS = {
  starter_monthly: process.env.STRIPE_PRICE_STARTER_MONTHLY || 'price_starter_12_monthly',
  starter_yearly: process.env.STRIPE_PRICE_STARTER_YEARLY || 'price_starter_120_yearly',
  premium_monthly: process.env.STRIPE_PRICE_PREMIUM_MONTHLY || 'price_premium_24_monthly',
  premium_yearly: process.env.STRIPE_PRICE_PREMIUM_YEARLY || 'price_premium_240_yearly',
  token_pack_100k: process.env.STRIPE_PRICE_TOKEN_PACK || 'price_token_pack_3',
  image_pack_1000: process.env.STRIPE_PRICE_IMAGE_PACK || 'price_image_pack_5',
} as const;

export const stripe = new Stripe(STRIPE_API_KEY, {
  apiVersion: '2025-04-30.basil',
  typescript: true,
});

export async function createStripeCustomer(email: string, name?: string) {
  try {
    const customer = await stripe.customers.create({
      email,
      name: name || email,
    });

    return customer;
  } catch (error) {
    log.error('Error creating Stripe customer:', error);
    throw error;
  }
}

export async function createStripeSession(
  customerId: string,
  plan: SubscriptionPlan,
  returnUrl: string
) {
  if (plan === 'free') {
    throw new Error('Cannot create subscription for free plan');
  }

  const priceId = STRIPE_PRICES[plan];
  if (!priceId) {
    throw new Error(`No price ID found for plan: ${plan}`);
  }

  try {
    const session = await stripe.checkout.sessions.create({
      customer: customerId,
      payment_method_types: ['card'],
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      mode: 'subscription',
      success_url: `${returnUrl}?success=true`,
      cancel_url: `${returnUrl}?canceled=true`,
    });

    return session;
  } catch (error) {
    log.error('Error creating Stripe session:', error);
    throw error;
  }
}

export async function manageSubscription(customerId: string, returnUrl: string) {
  try {
    const session = await stripe.billingPortal.sessions.create({
      customer: customerId,
      return_url: returnUrl,
    });

    return session;
  } catch (error) {
    log.error('Error creating billing portal session:', error);
    throw error;
  }
}

export async function cancelSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: true,
    });

    return subscription;
  } catch (error) {
    log.error('Error canceling subscription:', error);
    throw error;
  }
}

export async function reactivateSubscription(subscriptionId: string) {
  try {
    const subscription = await stripe.subscriptions.update(subscriptionId, {
      cancel_at_period_end: false,
    });

    return subscription;
  } catch (error) {
    log.error('Error reactivating subscription:', error);
    throw error;
  }
}

export async function upgradeSubscription(
  subscriptionId: string,
  plan: SubscriptionPlan
) {
  if (plan === 'free') {
    throw new Error('Cannot upgrade to free plan');
  }

  const priceId = STRIPE_PRICES[plan];
  if (!priceId) {
    throw new Error(`No price ID found for plan: ${plan}`);
  }

  try {
    const subscription = await stripe.subscriptions.retrieve(subscriptionId);

    // Find the current item ID
    const itemId = subscription.items.data[0].id;

    // Update the subscription with the new price
    const updatedSubscription = await stripe.subscriptions.update(subscriptionId, {
      items: [
        {
          id: itemId,
          price: priceId,
        },
      ],
    });

    return updatedSubscription;
  } catch (error) {
    log.error('Error upgrading subscription:', error);
    throw error;
  }
}

export function constructEventFromPayload(signature: string, payload: Buffer) {
  try {
    const event = stripe.webhooks.constructEvent(
      payload,
      signature,
      STRIPE_WEBHOOK_SECRET
    );

    return event;
  } catch (error) {
    log.error('Error constructing webhook event:', error);
    throw error;
  }
}