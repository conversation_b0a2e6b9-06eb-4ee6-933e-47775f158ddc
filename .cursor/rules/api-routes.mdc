---
description:
globs:
alwaysApply: false
---
# API Routes Guide

This guide covers the API route structure, patterns, and implementation details for the chatty-app backend.

## API Route Structure

### Route Organization
API routes are organized in [src/app/api/](mdc:src/app/api) following Next.js 15 App Router conventions:

```
/api/
├── auth/
│   └── callback/          # OAuth callback handling
├── chat/
│   ├── stream/           # WebSocket/streaming endpoints
│   └── stream-sse/       # Server-Sent Events streaming
├── conversations/        # Individual conversation management
│   ├── [id]/            # Specific conversation operations
│   ├── archive/         # Archive conversations
│   └── import-shared/   # Import shared conversations
├── groupConversations/  # Group conversation management
│   ├── [id]/           # Specific group conversation
│   ├── archive/        # Archive group conversations
│   └── bulk/           # Bulk operations
├── me/                 # User profile endpoints
│   ├── accessible-models/
│   └── quota/
├── models/             # Available AI models
├── providers/          # LLM provider information
├── workspaces/         # Workspace management
│   └── [id]/
└── upload/            # File upload handling
```

## Core API Patterns

### Route Handler Structure
All API routes follow this consistent pattern:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/utils/supabase/server';
import { logger } from '@/lib/logger';
import { z } from 'zod';

// Input validation schema
const RequestSchema = z.object({
  title: z.string().min(1).max(100),
  content: z.string().optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = createServerClient();

    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch data
    const { data, error } = await supabase
      .from('table_name')
      .select('*')
      .eq('id', params.id)
      .eq('user_id', user.id)
      .single();

    if (error) {
      logger.error('Database error:', { error, userId: user.id });
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    return NextResponse.json({ data });
  } catch (error) {
    logger.error('API error:', { error, route: '/api/example' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const body = await request.json();

    // Validate input
    const validatedData = RequestSchema.parse(body);

    // Authenticate user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Process request
    const { data, error } = await supabase
      .from('table_name')
      .insert({
        ...validatedData,
        user_id: user.id,
      })
      .select()
      .single();

    if (error) {
      logger.error('Database error:', { error, userId: user.id });
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    return NextResponse.json({ data }, { status: 201 });
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('API error:', { error, route: '/api/example' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Key API Endpoints

### Chat Streaming
#### `/api/chat/stream-sse/[messageId]`
Server-Sent Events endpoint for real-time chat streaming:

```typescript
// GET /api/chat/stream-sse/[messageId]
export async function GET(
  request: NextRequest,
  { params }: { params: { messageId: string } }
) {
  const { searchParams } = new URL(request.url);
  const conversationId = searchParams.get('conversationId');
  const model = searchParams.get('model');
  const useWebSearch = searchParams.get('useWebSearch') === 'true';
  const workspaceId = searchParams.get('workspaceId');

  // Create SSE stream
  const stream = new ReadableStream({
    start(controller) {
      // Send initial connection
      controller.enqueue(
        new TextEncoder().encode('data: {"type":"connected"}\n\n')
      );

      // Start LLM streaming
      streamLLMResponse({
        messageId: params.messageId,
        conversationId,
        model,
        useWebSearch,
        workspaceId,
        onDelta: (text, replace) => {
          controller.enqueue(
            new TextEncoder().encode(
              `data: ${JSON.stringify({ type: 'delta', content: text, replace })}\n\n`
            )
          );
        },
        onAnnotation: (annotation) => {
          controller.enqueue(
            new TextEncoder().encode(
              `data: ${JSON.stringify({ type: 'annotation', annotation })}\n\n`
            )
          );
        },
        onDone: () => {
          controller.enqueue(
            new TextEncoder().encode('data: {"type":"done"}\n\n')
          );
          controller.close();
        },
        onError: (error) => {
          controller.enqueue(
            new TextEncoder().encode(
              `data: ${JSON.stringify({ type: 'error', error: error.message })}\n\n`
            )
          );
          controller.close();
        },
      });
    },
  });

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    },
  });
}
```

### Conversation Management
#### `/api/groupConversations`
Manages group conversations with pagination:

```typescript
// GET /api/groupConversations
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const cursor = searchParams.get('cursor');
  const limit = parseInt(searchParams.get('limit') || '20', 10);
  const workspaceId = searchParams.get('workspaceId');

  try {
    const supabase = createServerClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    let query = supabase
      .from('group_conversations')
      .select('*')
      .eq('user_id', user.id)
      .order('updated_at', { ascending: false })
      .limit(limit + 1); // Fetch one extra for pagination

    if (workspaceId) {
      query = query.eq('workspace_id', workspaceId);
    }

    if (cursor) {
      query = query.lt('updated_at', cursor);
    }

    const { data: conversations, error } = await query;

    if (error) {
      logger.error('Database error:', { error, userId: user.id });
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    const hasMore = conversations.length > limit;
    const items = hasMore ? conversations.slice(0, -1) : conversations;
    const nextCursor = hasMore ? items[items.length - 1]?.updated_at : null;

    return NextResponse.json({
      conversations: items,
      nextCursor,
      hasMore,
    });
  } catch (error) {
    logger.error('API error:', { error, route: '/api/groupConversations' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### User Profile & Settings
#### `/api/me`
User profile and preferences:

```typescript
// GET /api/me
export async function GET() {
  try {
    const supabase = createServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Fetch user preferences
    const { data: preferences, error: prefError } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (prefError && prefError.code !== 'PGRST116') {
      logger.error('Preferences fetch error:', { error: prefError, userId: user.id });
    }

    // Fetch subscription data
    const { data: subscription, error: subError } = await supabase
      .from('subscriptions')
      .select('*')
      .eq('user_id', user.id)
      .single();

    if (subError && subError.code !== 'PGRST116') {
      logger.error('Subscription fetch error:', { error: subError, userId: user.id });
    }

    return NextResponse.json({
      user: {
        id: user.id,
        email: user.email,
        created_at: user.created_at,
      },
      preferences: preferences || null,
      subscription: subscription || null,
    });
  } catch (error) {
    logger.error('API error:', { error, route: '/api/me' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

### Model Access
#### `/api/me/accessible-models`
Returns models accessible to the current user based on subscription:

```typescript
// GET /api/me/accessible-models
export async function GET() {
  try {
    const supabase = createServerClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user subscription
    const { data: subscription } = await supabase
      .from('subscriptions')
      .select('plan')
      .eq('user_id', user.id)
      .single();

    const userPlan = subscription?.plan || 'free';
    const allowedTiers = PLAN_MODEL_ACCESS[userPlan];

    // Fetch accessible models
    const { data: models, error } = await supabase
      .from('llm_models')
      .select('id, name, tier, capabilities')
      .in('tier', allowedTiers)
      .eq('is_active', true);

    if (error) {
      logger.error('Models fetch error:', { error, userId: user.id });
      return NextResponse.json({ error: 'Database error' }, { status: 500 });
    }

    return NextResponse.json({ models });
  } catch (error) {
    logger.error('API error:', { error, route: '/api/me/accessible-models' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## File Upload Handling

### `/api/upload`
Handles file uploads with validation:

```typescript
// POST /api/upload
export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }

    // Validate file type and size
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return NextResponse.json({ error: 'File too large' }, { status: 400 });
    }

    const allowedTypes = ['image/', 'text/', 'application/pdf'];
    if (!allowedTypes.some(type => file.type.startsWith(type))) {
      return NextResponse.json({ error: 'File type not allowed' }, { status: 400 });
    }

    // Upload to storage
    const fileName = `${user.id}/${Date.now()}-${file.name}`;
    const { data, error } = await supabase.storage
      .from('uploads')
      .upload(fileName, file);

    if (error) {
      logger.error('Upload error:', { error, userId: user.id });
      return NextResponse.json({ error: 'Upload failed' }, { status: 500 });
    }

    // Get public URL
    const { data: { publicUrl } } = supabase.storage
      .from('uploads')
      .getPublicUrl(data.path);

    return NextResponse.json({ url: publicUrl });
  } catch (error) {
    logger.error('API error:', { error, route: '/api/upload' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Authentication & Authorization

### Supabase Auth Integration
All protected routes use Supabase authentication:

```typescript
import { createServerClient } from '@/utils/supabase/server';

const authenticateUser = async () => {
  const supabase = createServerClient();
  const { data: { user }, error } = await supabase.auth.getUser();

  if (error || !user) {
    throw new Error('Unauthorized');
  }

  return { user, supabase };
};
```

### Route Protection
Protected routes are configured in [src/middleware.ts](mdc:src/middleware.ts):

```typescript
export const config = {
  matcher: [
    '/auth/:path*',
    '/chat',
    '/c/:path*',
    '/settings',
    '/settings/:path*',
    '/api/chat/:path*',
    '/api/conversations/:path*',
    '/api/groupConversations/:path*',
  ],
};
```

## Error Handling Patterns

### Consistent Error Responses
All APIs return consistent error formats:

```typescript
// Validation errors
return NextResponse.json(
  { error: 'Validation error', details: zodError.errors },
  { status: 400 }
);

// Auth errors
return NextResponse.json(
  { error: 'Unauthorized' },
  { status: 401 }
);

// Database errors
return NextResponse.json(
  { error: 'Database error' },
  { status: 500 }
);

// Generic server errors
return NextResponse.json(
  { error: 'Internal server error' },
  { status: 500 }
);
```

### Logging Strategy
All errors are logged with context:

```typescript
import { logger } from '@/lib/logger';

logger.error('API error:', {
  error: error.message,
  stack: error.stack,
  route: request.url,
  method: request.method,
  userId: user?.id,
  timestamp: new Date().toISOString(),
});
```

## Testing API Routes

### API Route Testing
Use Jest and MSW for API testing:

```typescript
import { createMocks } from 'node-mocks-http';
import handler from '../route';

describe('/api/example', () => {
  it('should return data for authenticated user', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      headers: {
        authorization: 'Bearer valid-token',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    expect(JSON.parse(res._getData())).toEqual({
      data: expect.any(Object),
    });
  });
});
```
