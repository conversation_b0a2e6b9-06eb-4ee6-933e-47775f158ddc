---
description:
globs:
alwaysApply: false
---
# Chat System Architecture

This guide covers the chat system implementation, including message handling, streaming, model selection, and conversation management.

## Core Chat Components

### Main Chat Interface
- [src/components/chat/ChatArea.tsx](mdc:src/components/chat/ChatArea.tsx) - Main chat container with state management
- [src/components/chat/ChatContent.tsx](mdc:src/components/chat/ChatContent.tsx) - Message display and conversation view
- [src/components/chat/ChatInput.tsx](mdc:src/components/chat/ChatInput.tsx) - Message input with file upload support
- [src/components/chat/ChatHeader.tsx](mdc:src/components/chat/ChatHeader.tsx) - Header with model selection and settings

### Message Management
Messages use a tree structure to support branching conversations:

```typescript
// Message types from src/lib/supabase/types.ts
interface MessageNode {
  id: string;
  content: string;
  role: 'user' | 'assistant';
  children: MessageNode[];
  parent_message_id?: string;
  attachments?: AttachmentPayload[];
  annotations?: UrlCitationAnnotation[];
  metadata?: VersionedAssistantMessageMetadata;
}

// Tree traversal for flattened display
const getFlattenedMessages = (parentNode: MessageNode | null): MessageNode[] => {
  if (!parentNode) return [];

  const messages: MessageNode[] = [];
  const traverse = (node: MessageNode) => {
    messages.push(node);
    // Follow the selected branch or first child
    if (node.children.length > 0) {
      traverse(node.children[0]);
    }
  };

  traverse(parentNode);
  return messages;
};
```

## Chat State Management

### ChatProvider Context
[src/providers/ChatProvider.tsx](mdc:src/providers/ChatProvider.tsx) manages all chat state:

```typescript
interface ChatContextType {
  // Conversations
  loadedConversations: GroupConversation[];
  selectedConversation: GroupConversation | null;

  // Chat sessions (multiple models per conversation)
  chatSessions: ChatSession[];

  // Loading states
  isChatLoading: boolean;
  isConversationListLoading: boolean;

  // Streaming
  sseConnections: Map<string, StreamSubscription>;
  partialMessages: Map<string, PartialMessage>;

  // Actions
  fetchUserConversations: (isBackgroundRefresh?: boolean) => Promise<void>;
  initializeNewChat: (workspaceId?: string, promptDefaultModel?: string) => Promise<void>;
  subscribeToStream: (...) => void;
  unsubscribeFromStream: (messageId: string) => void;
}
```

### Chat Sessions
Each chat session represents a model within a conversation:

```typescript
interface ChatSession {
  id: string;
  model: Model;
  conversationId: string;
  conversationState: ConversationState;
  parentMessageNode: MessageNode | null;
}
```

## Model Selection System

### Model Selection Components
- [src/components/chat/ChatModelSelector/](mdc:src/components/chat/ChatModelSelector) - Model selection dropdown
- [src/components/chat/ModelSelector/](mdc:src/components/chat/ModelSelector) - Enhanced model selector with provider filtering

### Model Access Control
Models are filtered based on user subscription level:

```typescript
// From src/lib/supabase/types.ts
const PLAN_MODEL_ACCESS: Record<SubscriptionPlan, ModelTier[]> = {
  free: ['free'],
  starter: ['free', 'starter'],
  premium: ['free', 'starter', 'premium'],
};

// Usage in components
const { accessibleModelIds } = useSubscription();
const availableModels = allModels.filter(model =>
  accessibleModelIds.includes(model.id)
);
```

## Streaming Implementation

### Server-Sent Events (SSE)
Real-time message streaming uses SSE for better reliability:

```typescript
// Subscription pattern from ChatProvider
const subscribeToStream = useCallback((
  assistantMessageId: string,
  conversationId: string,
  model: string,
  onDelta: (text: string, replace?: boolean) => void,
  onAnnotation: (annotation: UrlCitationAnnotation) => void,
  onError: (error: Error) => void,
  onDone: () => void,
  onMetadata: (metadata: StreamMetadataClientData) => void
) => {
  const eventSource = new EventSource(
    `/api/chat/stream-sse/${assistantMessageId}?` +
    new URLSearchParams({
      conversationId,
      model,
      useWebSearch: useWebSearch.toString(),
      useImageGeneration: useImageGeneration.toString(),
      workspaceId: workspaceId || '',
    })
  );

  const subscription: StreamSubscription = {
    eventSource,
    onDelta,
    onAnnotation,
    onError,
    onDone,
    onMetadata,
    groupConversationId: conversationId,
  };

  setSseConnections(prev => new Map(prev).set(assistantMessageId, subscription));
}, []);
```

### Partial Message Handling
Streaming content is stored temporarily before being persisted:

```typescript
interface PartialMessage {
  content: string;
  annotations: UrlCitationAnnotation[];
}

// Update partial message during streaming
const updatePartialMessage = (messageId: string, content: string) => {
  setPartialMessages(prev => {
    const newMap = new Map(prev);
    const existing = newMap.get(messageId) || { content: '', annotations: [] };
    newMap.set(messageId, { ...existing, content });
    return newMap;
  });
};
```

## Chat Hooks Architecture

### Custom Hooks for Chat Features
Located in [src/components/chat/hooks/](mdc:src/components/chat/hooks):

- `useChatHistory` - Loads conversation history
- `useChatModels` - Manages model capabilities and settings
- `useMessageTree` - Handles message tree navigation
- `useSendMessage` - Handles message sending and file uploads
- `usePartialMessages` - Applies partial messages to display
- `useAttachmentUpload` - File upload functionality

### Hook Composition Pattern
Hooks are composed in ChatArea for clean separation of concerns:

```typescript
const ChatArea = ({ groupConversationId, isTemporary }) => {
  // Load conversation history
  const { isLoading: isLoadingHistory } = useChatHistory({
    chatSessions,
    setChatSessions,
    groupConversationId,
  });

  // Model capabilities
  const {
    canSearch,
    canUpload,
    useWebSearch,
    setUseWebSearch,
    canGenerateImages,
    useImageGeneration,
    setUseImageGeneration,
  } = useChatModels();

  // Message tree navigation
  const { getFlattenedMessages, handleBranchChange } = useMessageTree();

  // Sending messages
  const {
    handleSendMessage,
    handleRetryMessage,
    handleUpload,
    isUploading,
    editMessage,
  } = useSendMessage({
    chatSessions,
    setChatSessions,
    groupConversationId,
    getFlattenedMessages,
    providers,
    useWebSearch,
    useImageGeneration,
  });
};
```

## File Upload System

### Attachment Handling
Files are uploaded before sending messages:

```typescript
interface AttachmentPayload {
  name: string;
  type: string;
  url: string;
  image_url?: {
    url: string;
    detail: 'auto' | 'low' | 'high';
  };
}

// Upload process
const handleFileUpload = async (files: File[]) => {
  const uploadPromises = files.map(async (file) => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    const { url } = await response.json();
    return {
      name: file.name,
      type: file.type,
      url,
    };
  });

  return Promise.all(uploadPromises);
};
```

## Web Search & Image Generation

### Capability-Based Features
Features are enabled based on model capabilities and user subscription:

```typescript
// Model capabilities
interface ModelCapabilities {
  file_upload: boolean;
  web_search: boolean;
  image_generation: boolean;
  visible_by_default: boolean;
}

// Feature enablement
const canUseWebSearch = useMemo(() => {
  return selectedModel?.capabilities?.web_search &&
         subscriptionAllowsWebSearch;
}, [selectedModel, subscriptionAllowsWebSearch]);
```

## Conversation Management

### Group Conversations
Conversations are organized as group conversations that can contain multiple chat sessions:

```typescript
interface GroupConversation {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  workspace_id?: string;
  is_temporary: boolean;
  is_favorite: boolean;
}
```

### Conversation Actions
- Create new conversations
- Archive/delete conversations
- Search conversations
- Bulk operations on conversations
- Share conversations publicly

## Performance Optimizations

### Message Rendering
- Use React.memo for message components
- Virtualize long conversation histories with `@tanstack/react-virtual`
- Lazy load conversation history on scroll

### Streaming Optimization
- Buffer rapid message updates
- Debounce UI updates during streaming
- Clean up SSE connections on unmount

### Memory Management
- Clear partial messages after completion
- Limit conversation history in memory
- Use pagination for conversation lists

## Testing Chat Components

### Test Structure
Chat tests are in [src/components/chat/__tests__/](mdc:src/components/chat/__tests__):

```typescript
// Example test for ChatArea
describe('ChatArea', () => {
  it('should render chat interface', () => {
    const mockChatSessions = [mockChatSession];

    render(
      <ChatProvider>
        <ChatArea
          groupConversationId="test-id"
          isTemporary={false}
          setIsTemporary={jest.fn()}
          chatState="chat"
        />
      </ChatProvider>
    );

    expect(screen.getByRole('textbox')).toBeInTheDocument();
  });
});
```

### Mocking Strategies
- Mock SSE connections in tests
- Use MSW for API mocking in [src/mocks/](mdc:src/mocks)
- Mock provider contexts for isolated testing
