---
description:
globs:
alwaysApply: false
---
# Development Patterns & Best Practices

This guide covers the common patterns, conventions, and best practices used throughout the chatty-app codebase.

## Component Patterns

### React Component Structure
Components follow a consistent structure with memo optimization for performance:

```typescript
import React, { useState, useCallback, useContext } from 'react';
import { SomeContext } from '@/providers/SomeProvider';

interface Props {
  // Use discriminated unions for complex prop types
  mode: 'chat' | 'workspace';
  onAction?: () => void;
}

const MyComponent = React.memo((props: Props) => {
  const { mode, onAction } = props;
  const { someState } = useContext(SomeContext);

  const handleAction = useCallback(() => {
    onAction?.();
  }, [onAction]);

  return (
    <div className="flex flex-col">
      {/* Component content */}
    </div>
  );
});

MyComponent.displayName = 'MyComponent';
export default MyComponent;
```

### Provider Pattern
All providers follow a consistent structure with context and hooks:

```typescript
interface SomeContextType {
  state: SomeState;
  actions: {
    doSomething: () => void;
  };
}

export const SomeContext = createContext<SomeContextType>({
  // Default values
});

export function SomeProvider({ children }: { children: React.ReactNode }) {
  const [state, setState] = useState<SomeState>(initialState);

  const doSomething = useCallback(() => {
    // Action implementation
  }, []);

  const value = useMemo(() => ({
    state,
    actions: { doSomething }
  }), [state, doSomething]);

  return (
    <SomeContext.Provider value={value}>
      {children}
    </SomeContext.Provider>
  );
}
```

## Custom Hooks Patterns

### Data Fetching Hooks
Located in [src/components/chat/hooks/](mdc:src/components/chat/hooks) and follow this pattern:

```typescript
export function useDataFetching() {
  const [data, setData] = useState<DataType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const result = await api.fetchData();
      setData(result);
      setError(null);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  }, []);

  return { data, isLoading, error, fetchData };
}
```

### State Management Hooks
Use Zustand for complex state management:

```typescript
interface StoreState {
  items: Item[];
  actions: {
    addItem: (item: Item) => void;
    removeItem: (id: string) => void;
  };
}

export const useStore = create<StoreState>()((set) => ({
  items: [],
  actions: {
    addItem: (item) => set((state) => ({
      items: [...state.items, item]
    })),
    removeItem: (id) => set((state) => ({
      items: state.items.filter(item => item.id !== id)
    })),
  },
}));
```

## API Route Patterns

### Route Handler Structure
API routes in [src/app/api/](mdc:src/app/api) follow this pattern:

```typescript
import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function GET(request: NextRequest) {
  try {
    // Route logic here
    const data = await fetchData();

    return NextResponse.json({ data });
  } catch (error) {
    logger.error('API error:', { error, route: '/api/example' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    // Validate input with Zod
    const validatedData = schema.parse(body);

    const result = await processData(validatedData);
    return NextResponse.json({ result });
  } catch (error) {
    // Handle different error types
    if (error instanceof ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.errors },
        { status: 400 }
      );
    }

    logger.error('API error:', { error, route: '/api/example' });
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
```

## Error Handling

### Global Error Boundary
Use [src/components/ErrorBoundary.tsx](mdc:src/components/ErrorBoundary.tsx) for React error boundaries.

### Async Error Handling
Always wrap async operations with proper error handling:

```typescript
const handleAsyncAction = useCallback(async () => {
  try {
    setIsLoading(true);
    await asyncOperation();
  } catch (error) {
    logger.error('Operation failed:', { error });
    // Show user-friendly error message
    toast.error('Something went wrong. Please try again.');
  } finally {
    setIsLoading(false);
  }
}, []);
```

## Streaming & Real-time Updates

### Server-Sent Events (SSE)
The app uses SSE for real-time chat streaming. Pattern used in [src/providers/ChatProvider.tsx](mdc:src/providers/ChatProvider.tsx):

```typescript
const subscribeToStream = useCallback((
  messageId: string,
  onDelta: (text: string) => void,
  onError: (error: Error) => void,
  onDone: () => void
) => {
  const eventSource = new EventSource(`/api/chat/stream-sse/${messageId}`);

  eventSource.onmessage = (event) => {
    const data = JSON.parse(event.data);
    if (data.type === 'delta') {
      onDelta(data.content);
    } else if (data.type === 'done') {
      onDone();
      eventSource.close();
    }
  };

  eventSource.onerror = (error) => {
    onError(new Error('Stream error'));
    eventSource.close();
  };

  return () => eventSource.close();
}, []);
```

## Type Safety Patterns

### Database Types
Use generated types from [src/types/database.types.ts](mdc:src/types/database.types.ts) and extend them in [src/lib/supabase/types.ts](mdc:src/lib/supabase/types.ts):

```typescript
import { Tables } from '@/types/database.types';

export type BaseMessage = Tables<'messages'>;

export type Message = Omit<BaseMessage, 'attachments'> & {
  attachments: AttachmentPayload[] | null;
};
```

### Discriminated Unions
Use discriminated unions for component props with different modes:

```typescript
interface BaseProps {
  title: string;
}

interface ChatProps extends BaseProps {
  mode: 'chat';
  conversationId: string;
}

interface WorkspaceProps extends BaseProps {
  mode: 'workspace';
  workspaceId: string;
}

type ComponentProps = ChatProps | WorkspaceProps;
```

## Performance Patterns

### React.memo Usage
Wrap components that receive complex props or render frequently:

```typescript
const ExpensiveComponent = React.memo(({ data, onAction }: Props) => {
  // Component implementation
}, (prevProps, nextProps) => {
  // Custom comparison if needed
  return prevProps.data.id === nextProps.data.id;
});
```

### Callback Optimization
Use useCallback for functions passed to child components:

```typescript
const handleItemClick = useCallback((itemId: string) => {
  // Handle click
}, [/* dependencies */]);
```

### Virtualization
For large lists, use `@tanstack/react-virtual` as seen in the dependencies.

## Styling Patterns

### Tailwind CSS Classes
Use consistent class patterns with CSS variables for theming:

```typescript
const Button = ({ variant = 'default', ...props }) => {
  const variants = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
    destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
  };

  return (
    <button
      className={cn(
        'inline-flex items-center justify-center rounded-md px-4 py-2 transition-colors',
        variants[variant]
      )}
      {...props}
    />
  );
};
```

### Class Composition
Use `clsx` or `tailwind-merge` for conditional classes:

```typescript
import { cn } from '@/lib/utils';

const className = cn(
  'base-classes',
  isActive && 'active-classes',
  variant === 'primary' && 'primary-classes'
);
```

## Testing Patterns

### Component Testing
Tests are located in `__tests__` directories. Use React Testing Library:

```typescript
import { render, screen, fireEvent } from '@testing-library/react';
import { MyComponent } from './MyComponent';

describe('MyComponent', () => {
  it('should render correctly', () => {
    render(<MyComponent title="Test" />);
    expect(screen.getByText('Test')).toBeInTheDocument();
  });

  it('should handle click events', () => {
    const onClick = jest.fn();
    render(<MyComponent onClick={onClick} />);

    fireEvent.click(screen.getByRole('button'));
    expect(onClick).toHaveBeenCalled();
  });
});
```

## Security Patterns

### Input Validation
Always validate API inputs with Zod schemas:

```typescript
import { z } from 'zod';

const CreateConversationSchema = z.object({
  title: z.string().min(1).max(100),
  workspaceId: z.string().uuid().optional(),
});

// In API route
const validatedData = CreateConversationSchema.parse(body);
```

### Authentication
Use Supabase auth patterns and middleware for protection:
- [src/middleware.ts](mdc:src/middleware.ts) - Route protection
- [src/providers/AuthProvider.tsx](mdc:src/providers/AuthProvider.tsx) - Auth state management
