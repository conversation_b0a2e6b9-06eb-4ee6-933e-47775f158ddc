---
description:
globs:
alwaysApply: false
---
# Project Structure Guide

This is a Next.js 15 chat application called "Sabi Chat" built with TypeScript, React 19, and Supabase. The app provides AI chat functionality with multiple LLM providers, workspaces, and user management.

## Core Architecture

### Main Application Entry Points
- [src/app/layout.tsx](mdc:src/app/layout.tsx) - Root layout with provider hierarchy
- [src/app/(main)/layout.tsx](mdc:src/app/(main)/layout.tsx) - Protected routes layout with sidebar
- [src/middleware.ts](mdc:src/middleware.ts) - Auth middleware for protected routes

### Key Provider Structure
The app uses a hierarchical provider structure in [src/app/layout.tsx](mdc:src/app/layout.tsx):
1. `AppProvider` - Global app state
2. `ThemeProvider` - Dark/light theme management
3. `AuthProvider` - User authentication
4. `ChatProvider` - Chat state and conversations
5. `SidebarProvider` - Sidebar visibility state
6. `AnalyticsProvider` - User analytics
7. `BuilderProvider` - App builder functionality

## Core Routes Structure

### Public Routes
- `/auth/login` - User login page
- `/auth/details` - Additional user details
- `/auth/verify` - Email verification
- `/share/[id]` - Public conversation sharing

### Protected Routes (under /(main)/)
- `/chat` - Main chat interface
- `/chat/[id]` - Specific conversation
- `/chat/search` - Search conversations
- `/c/[id]` - Alternative conversation route
- `/chats` - Conversation list
- `/prompts` - Prompt library
- `/workspaces` - Workspace management
- `/workspaces/[id]` - Specific workspace
- `/settings` - User settings

## Key Components

### Chat System
- [src/components/chat/ChatArea.tsx](mdc:src/components/chat/ChatArea.tsx) - Main chat interface
- [src/components/chat/ChatContent.tsx](mdc:src/components/chat/ChatContent.tsx) - Message display
- [src/components/chat/ChatInput.tsx](mdc:src/components/chat/ChatInput.tsx) - Message input
- [src/components/chat/ChatHeader.tsx](mdc:src/components/chat/ChatHeader.tsx) - Chat header with model selection

### Sidebar & Navigation
- [src/components/sidebar/AppSidebar.tsx](mdc:src/components/sidebar/AppSidebar.tsx) - Main sidebar component
- [src/components/sidebar/ConversationList.tsx](mdc:src/components/sidebar/ConversationList.tsx) - Conversation listing
- [src/components/sidebar/ConversationItem.tsx](mdc:src/components/sidebar/ConversationItem.tsx) - Individual conversation item

### UI Components
- [src/components/ui/](mdc:src/components/ui) - Shadcn/ui components (35+ components)
- [components.json](mdc:components.json) - Shadcn/ui configuration

## State Management

### Main Providers
- [src/providers/ChatProvider.tsx](mdc:src/providers/ChatProvider.tsx) - Chat state, conversations, streaming
- [src/providers/AuthProvider.tsx](mdc:src/providers/AuthProvider.tsx) - User authentication state
- [src/providers/SidebarProvider.tsx](mdc:src/providers/SidebarProvider.tsx) - Sidebar visibility
- [src/providers/AppProvider.tsx](mdc:src/providers/AppProvider.tsx) - Global app state

### Zustand Stores
- [src/stores/builderStore.ts](mdc:src/stores/builderStore.ts) - App builder state

## Database & Types

### Supabase Integration
- [src/lib/supabase/](mdc:src/lib/supabase) - Supabase client and utilities
- [src/types/database.types.ts](mdc:src/types/database.types.ts) - Generated database types
- [src/lib/supabase/types.ts](mdc:src/lib/supabase/types.ts) - Extended types and interfaces

### Key Types
- `GroupConversation` - Chat conversations
- `Message` & `MessageNode` - Chat messages with tree structure
- `Provider` & `Model` - LLM providers and models
- `Workspace` - User workspaces
- `ConversationState` - Chat state management

## Services

### LLM Integration
- [src/services/llm/](mdc:src/services/llm) - LLM provider abstraction
- [src/services/llm/providers/](mdc:src/services/llm/providers) - Individual LLM providers (Anthropic, OpenAI, etc.)
- [src/services/llm/factory.ts](mdc:src/services/llm/factory.ts) - Provider factory

### Core Services
- [src/services/modelAccess.ts](mdc:src/services/modelAccess.ts) - Model access control
- [src/services/tokenizer/](mdc:src/services/tokenizer) - Token counting
- [src/services/anonymousSession.ts](mdc:src/services/anonymousSession.ts) - Anonymous user sessions

## API Routes

### Chat API
- `/api/chat/stream` - Streaming chat responses
- `/api/chat/stream-sse` - Server-sent events streaming

### Data API
- `/api/conversations/` - Conversation CRUD
- `/api/groupConversations/` - Group conversation management
- `/api/workspaces/` - Workspace management
- `/api/models/` - Available models
- `/api/providers/` - LLM providers

### User API
- `/api/me/` - User profile and settings
- `/api/me/accessible-models` - User's accessible models
- `/api/me/quota` - Usage quotas

## Configuration Files

### Build & Dev
- [package.json](mdc:package.json) - Dependencies and scripts
- [next.config.ts](mdc:next.config.ts) - Next.js configuration
- [tsconfig.json](mdc:tsconfig.json) - TypeScript configuration
- [tailwind.config.ts](mdc:tailwind.config.ts) - Tailwind CSS configuration

### Testing
- [jest.config.ts](mdc:jest.config.ts) - Jest testing configuration
- [jest.setup.ts](mdc:jest.setup.ts) - Test setup
- [src/components/chat/__tests__/](mdc:src/components/chat/__tests__) - Chat component tests

### Linting & Quality
- [eslint.config.mjs](mdc:eslint.config.mjs) - ESLint configuration
- [scripts/lint-changed.sh](mdc:scripts/lint-changed.sh) - Lint changed files script

## Key Constants
- [src/constants/chat.ts](mdc:src/constants/chat.ts) - Chat-related constants
- [src/constants/attachment.ts](mdc:src/constants/attachment.ts) - File attachment constants

## Styling
- Uses Tailwind CSS with CSS variables for theming
- [src/app/globals.css](mdc:src/app/globals.css) - Global styles and CSS variables
- Supports dark/light themes via `next-themes`
- Design system based on Shadcn/ui components

## Development Scripts
- `yarn dev` - Development server with Turbopack
- `yarn build` - Production build
- `yarn test` - Run Jest tests
- `yarn lint` - Run ESLint
- `yarn format` - Format with Prettier
