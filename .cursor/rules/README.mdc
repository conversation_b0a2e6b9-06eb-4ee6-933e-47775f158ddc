---
description:
globs:
alwaysApply: false
---
# Chatty-App Cursor Rules

Welcome to the comprehensive Cursor rules for **Sabi Chat** - a Next.js 15 AI chat application. These rules provide detailed guidance for navigating, understanding, and contributing to the codebase.

## Quick Navigation

### 🏗️ Architecture & Structure
- **[Project Structure](mdc:.cursor/rules/project-structure.mdc)** - Complete overview of the codebase organization
- **[Database Schema](mdc:.cursor/rules/database-schema.mdc)** - Supabase database tables, relationships, and patterns
- **[API Routes](mdc:.cursor/rules/api-routes.mdc)** - Backend API structure and implementation patterns

### 💻 Development Guides
- **[Development Patterns](mdc:.cursor/rules/development-patterns.mdc)** - Coding patterns, best practices, and conventions
- **[Chat System](mdc:.cursor/rules/chat-system.mdc)** - Chat components, streaming, and conversation management
- **[Sentry Error Monitoring](mdc:.cursor/rules/sentry.mdc)** - Error tracking and monitoring implementation

## Technology Stack

### Core Technologies
- **Next.js 15** with App Router
- **React 19** with TypeScript
- **Supabase** for database and authentication
- **Tailwind CSS** for styling
- **Shadcn/ui** for UI components

### Key Libraries
- **Zustand** - State management
- **React Hook Form** - Form handling
- **Zod** - Schema validation
- **Framer Motion** - Animations
- **Monaco Editor** - Code editing
- **React Markdown** - Markdown rendering

### AI & LLM Integration
- **Multiple LLM Providers** - OpenAI, Anthropic, Google Gemini, DeepSeek
- **Streaming Support** - Server-Sent Events for real-time responses
- **Model Access Control** - Subscription-based model access
- **File Upload Support** - Document and image processing

## Getting Started

### Understanding the Codebase
1. Start with [Project Structure](mdc:.cursor/rules/project-structure.mdc) for overall organization
2. Review [Development Patterns](mdc:.cursor/rules/development-patterns.mdc) for coding conventions
3. Explore [Chat System](mdc:.cursor/rules/chat-system.mdc) for core functionality
4. Reference [Database Schema](mdc:.cursor/rules/database-schema.mdc) for data relationships

### Key Entry Points
- [src/app/layout.tsx](mdc:src/app/layout.tsx) - Root application layout
- [src/app/(main)/layout.tsx](mdc:src/app/(main)/layout.tsx) - Protected routes layout
- [src/providers/ChatProvider.tsx](mdc:src/providers/ChatProvider.tsx) - Chat state management
- [src/components/chat/ChatArea.tsx](mdc:src/components/chat/ChatArea.tsx) - Main chat interface

### Development Commands
```bash
# Development server with Turbopack
yarn dev

# Production build
yarn build

# Run tests
yarn test

# Lint code
yarn lint

# Format code
yarn format
```

## Architecture Overview

### Provider Hierarchy
The app uses a nested provider structure for state management:
```
AppProvider
├── ThemeProvider (dark/light themes)
├── AuthProvider (user authentication)
├── ChatProvider (chat state & conversations)
├── SidebarProvider (sidebar visibility)
├── AnalyticsProvider (user analytics)
└── BuilderProvider (app builder functionality)
```

### Route Structure
```
/(main)/                 # Protected routes
├── /chat               # Main chat interface
├── /c/[id]            # Specific conversations
├── /chats             # Conversation list
├── /prompts           # Prompt library
├── /workspaces        # Workspace management
└── /settings          # User settings

/auth/                  # Authentication routes
├── /login             # User login
├── /details           # User details
└── /verify            # Email verification

/share/[id]            # Public conversation sharing
```

### Database Structure
```
Users & Auth
├── auth.users (Supabase)
├── user_preferences
├── subscriptions
└── user_daily_usage

Chat System
├── group_conversations
├── conversations
├── messages
├── llm_providers
└── llm_models

Workspaces
├── workspaces
├── workspace_files
└── workspace_notes

Content
├── prompts
├── uploaded_files
└── usage_logs
```

## Key Features

### 🤖 Multi-Model Chat
- Support for multiple LLM providers (OpenAI, Anthropic, Google, DeepSeek)
- Real-time streaming with Server-Sent Events
- Model comparison within single conversations
- Subscription-based model access control

### 💬 Conversation Management
- Tree-structured messages for branching conversations
- Conversation organization with workspaces
- Favorite and archive functionality
- Search across conversations
- Public conversation sharing

### 📁 Workspace System
- File uploads and management
- Note-taking within workspaces
- Context-aware conversations
- Organized project structure

### 🔒 Security & Auth
- Supabase authentication
- Row Level Security (RLS) policies
- API route protection with middleware
- Input validation with Zod schemas

### 📊 Analytics & Monitoring
- Sentry error monitoring and performance tracking
- Usage analytics with June.so
- Token and quota tracking
- Daily usage limits

## Common Patterns

### Component Development
```typescript
// Use React.memo for performance
const MyComponent = React.memo(({ prop1, prop2 }: Props) => {
  const { state } = useContext(SomeContext);

  const handleAction = useCallback(() => {
    // Action logic
  }, [dependencies]);

  return <div className="flex flex-col">{/* content */}</div>;
});
```

### Custom Hooks
```typescript
// Data fetching pattern
export function useDataFetching() {
  const [data, setData] = useState<DataType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const fetchData = useCallback(async () => {
    // Fetch logic with error handling
  }, []);

  return { data, isLoading, error, fetchData };
}
```

### API Routes
```typescript
// Consistent API route structure
export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient();
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Route logic here

    return NextResponse.json({ data });
  } catch (error) {
    logger.error('API error:', { error, route: '/api/example' });
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
```

## Testing Strategy

### Component Testing
- Tests in `__tests__` directories
- React Testing Library for component testing
- MSW for API mocking
- Provider mocking for isolated testing

### API Testing
- Jest for unit tests
- Integration tests for API routes
- Database testing with test fixtures

## Performance Considerations

### React Optimization
- React.memo for expensive components
- useCallback for stable function references
- Virtualization for large lists
- Code splitting for route-based chunks

### Database Optimization
- Proper indexing strategy
- Pagination for large datasets
- Select only needed columns
- RLS policies for security

### Streaming Optimization
- SSE for real-time updates
- Partial message buffering
- Connection cleanup on unmount
- Error handling and reconnection

## Styling Guidelines

### Tailwind CSS
- Use CSS variables for theming
- Consistent class patterns
- Mobile-first responsive design
- Dark/light theme support

### Component Styling
```typescript
// Use cn utility for class composition
const className = cn(
  'base-classes',
  isActive && 'active-classes',
  variant === 'primary' && 'primary-variant'
);
```

## Error Handling

### Global Error Handling
- React Error Boundaries for component errors
- API error standardization
- Sentry integration for monitoring
- User-friendly error messages

### Logging Strategy
```typescript
import { logger } from '@/lib/logger';

logger.error('Operation failed:', {
  error: error.message,
  context: { userId, action },
  timestamp: new Date().toISOString(),
});
```

## Contributing Guidelines

### Code Style
- Follow existing patterns documented in these rules
- Use TypeScript for type safety
- Implement proper error handling
- Add tests for new features

### Pull Request Process
1. Follow the development patterns in these rules
2. Ensure all tests pass
3. Update documentation if needed
4. Get code review approval

### Debugging
- Use browser DevTools for client-side debugging
- Check Sentry for error monitoring
- Use Supabase dashboard for database issues
- Monitor API logs for backend problems

---

These rules provide comprehensive guidance for working with the Sabi Chat codebase. Each rule file contains detailed information and examples for specific areas of the application. Use them as reference when developing, debugging, or extending the application.
