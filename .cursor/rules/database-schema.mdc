---
description:
globs:
alwaysApply: false
---
# Database Schema Guide

This guide covers the Supabase database schema, key tables, relationships, and data access patterns used in the chatty-app.

## Database Overview

The application uses **Supabase PostgreSQL** as the primary database with Row Level Security (RLS) policies for data protection. All types are generated in [src/types/database.types.ts](mdc:src/types/database.types.ts) and extended in [src/lib/supabase/types.ts](mdc:src/lib/supabase/types.ts).

## Core Tables

### Users & Authentication

#### `auth.users` (Supabase Auth)
Built-in Supabase authentication table:
```sql
-- Managed by Supabase Auth
users (
  id uuid PRIMARY KEY,
  email text UNIQUE,
  created_at timestamptz,
  updated_at timestamptz,
  -- Additional auth fields...
)
```

#### `user_preferences`
User settings and preferences:
```sql
user_preferences (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  theme text DEFAULT 'system',
  language text DEFAULT 'en',
  code_theme text DEFAULT 'github-dark',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

### Subscription & Billing

#### `subscriptions`
User subscription management:
```sql
subscriptions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  plan subscription_plan DEFAULT 'free',
  status subscription_status DEFAULT 'active',
  current_period_start timestamptz,
  current_period_end timestamptz,
  stripe_subscription_id text,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- Enums
CREATE TYPE subscription_plan AS ENUM ('free', 'starter', 'premium');
CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'past_due', 'unpaid');
```

#### `user_daily_usage`
Tracks daily usage for quota management:
```sql
user_daily_usage (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  date date DEFAULT current_date,
  message_count integer DEFAULT 0,
  token_count integer DEFAULT 0,
  image_generation_count integer DEFAULT 0,
  UNIQUE(user_id, date)
)
```

## Chat System Tables

### Conversations

#### `group_conversations`
Main conversation containers:
```sql
group_conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  workspace_id uuid REFERENCES workspaces(id) ON DELETE SET NULL,
  title text NOT NULL,
  is_temporary boolean DEFAULT false,
  is_favorite boolean DEFAULT false,
  is_archived boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

#### `conversations`
Individual model conversations within a group:
```sql
conversations (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  group_conversation_id uuid REFERENCES group_conversations(id) ON DELETE CASCADE,
  model_id uuid REFERENCES llm_models(id),
  state conversation_state DEFAULT 'active',
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- Enum for conversation state
CREATE TYPE conversation_state AS ENUM ('active', 'paused', 'completed', 'error');
```

### Messages

#### `messages`
Message storage with tree structure:
```sql
messages (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id uuid REFERENCES conversations(id) ON DELETE CASCADE,
  parent_message_id uuid REFERENCES messages(id) ON DELETE SET NULL,
  role message_role NOT NULL,
  content text NOT NULL,
  attachments jsonb DEFAULT '[]',
  annotations jsonb DEFAULT '[]',
  file_annotations jsonb DEFAULT '[]',
  metadata jsonb,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- Enum for message roles
CREATE TYPE message_role AS ENUM ('user', 'assistant', 'system');
```

## LLM & AI Models

#### `llm_providers`
AI service providers:
```sql
llm_providers (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  name text NOT NULL UNIQUE,
  display_name text NOT NULL,
  api_base_url text,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

#### `llm_models`
Available AI models:
```sql
llm_models (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  provider_id uuid REFERENCES llm_providers(id) ON DELETE CASCADE,
  name text NOT NULL,
  display_name text NOT NULL,
  tier model_tier DEFAULT 'free',
  capabilities jsonb DEFAULT '{}',
  supported_parameters text[],
  provider_specific_data jsonb DEFAULT '{}',
  architecture jsonb,
  pricing jsonb,
  is_active boolean DEFAULT true,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)

-- Enum for model tiers
CREATE TYPE model_tier AS ENUM ('free', 'starter', 'premium');
```

## Workspace System

#### `workspaces`
User workspaces for organizing conversations:
```sql
workspaces (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  description text,
  is_favorite boolean DEFAULT false,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

#### `workspace_files`
Files uploaded to workspaces:
```sql
workspace_files (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id uuid REFERENCES workspaces(id) ON DELETE CASCADE,
  name text NOT NULL,
  type text NOT NULL,
  size bigint NOT NULL,
  url text NOT NULL,
  metadata jsonb DEFAULT '{}',
  created_at timestamptz DEFAULT now()
)
```

#### `workspace_notes`
Text notes within workspaces:
```sql
workspace_notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id uuid REFERENCES workspaces(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text NOT NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

## Prompt Library

#### `prompts`
Reusable prompt templates:
```sql
prompts (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  title text NOT NULL,
  content text NOT NULL,
  description text,
  tags text[] DEFAULT '{}',
  is_public boolean DEFAULT false,
  is_favorite boolean DEFAULT false,
  default_model_id uuid REFERENCES llm_models(id) ON DELETE SET NULL,
  created_at timestamptz DEFAULT now(),
  updated_at timestamptz DEFAULT now()
)
```

## File Management

#### `uploaded_files`
General file uploads:
```sql
uploaded_files (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  name text NOT NULL,
  type text NOT NULL,
  size bigint NOT NULL,
  url text NOT NULL,
  created_at timestamptz DEFAULT now()
)
```

## Usage Tracking

#### `usage_logs`
Detailed usage tracking:
```sql
usage_logs (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES auth.users(id) ON DELETE CASCADE,
  conversation_id uuid REFERENCES conversations(id) ON DELETE SET NULL,
  model_id uuid REFERENCES llm_models(id) ON DELETE SET NULL,
  prompt_tokens integer DEFAULT 0,
  completion_tokens integer DEFAULT 0,
  total_tokens integer DEFAULT 0,
  cost numeric(10,6) DEFAULT 0,
  created_at timestamptz DEFAULT now()
)
```

## Key Relationships

### Message Tree Structure
Messages form a tree structure for conversation branching:
```typescript
// Message tree traversal
const getMessageTree = (parentId: string | null): MessageNode[] => {
  // Get all children of the parent
  const children = messages.filter(msg => msg.parent_message_id === parentId);

  return children.map(message => ({
    ...message,
    children: getMessageTree(message.id)
  }));
};
```

### Conversation Hierarchy
```
User
├── GroupConversation (main chat container)
│   ├── Conversation (model 1)
│   │   └── Messages (tree structure)
│   └── Conversation (model 2)
│       └── Messages (tree structure)
└── Workspace
    ├── GroupConversation
    ├── Files
    └── Notes
```

## Row Level Security (RLS)

### User Data Isolation
All tables have RLS policies to ensure users can only access their own data:

```sql
-- Example RLS policy for group_conversations
CREATE POLICY "Users can only see their own conversations"
ON group_conversations
FOR ALL
USING (auth.uid() = user_id);

-- Example RLS policy for messages through conversation ownership
CREATE POLICY "Users can only see messages from their conversations"
ON messages
FOR ALL
USING (
  EXISTS (
    SELECT 1 FROM conversations c
    JOIN group_conversations gc ON c.group_conversation_id = gc.id
    WHERE c.id = messages.conversation_id
    AND gc.user_id = auth.uid()
  )
);
```

## Data Access Patterns

### Supabase Client Usage
Use the appropriate client based on context:

```typescript
// Server-side (API routes)
import { createServerClient } from '@/utils/supabase/server';

// Client-side (components)
import { createClient } from '@/utils/supabase/client';

// Middleware
import { createMiddlewareClient } from '@/utils/supabase/middleware';
```

### Common Query Patterns

#### Fetch User Conversations with Pagination
```typescript
const { data: conversations, error } = await supabase
  .from('group_conversations')
  .select(`
    *,
    workspace:workspaces(name)
  `)
  .eq('user_id', user.id)
  .order('updated_at', { ascending: false })
  .range(0, 19); // Pagination
```

#### Fetch Conversation with Messages
```typescript
const { data: conversation, error } = await supabase
  .from('group_conversations')
  .select(`
    *,
    conversations(
      *,
      messages(
        *,
        model:llm_models(name, display_name)
      )
    )
  `)
  .eq('id', conversationId)
  .eq('user_id', user.id)
  .single();
```

#### Check Model Access
```typescript
const { data: subscription } = await supabase
  .from('subscriptions')
  .select('plan')
  .eq('user_id', user.id)
  .single();

const userPlan = subscription?.plan || 'free';
const allowedTiers = PLAN_MODEL_ACCESS[userPlan];

const { data: models } = await supabase
  .from('llm_models')
  .select('*')
  .in('tier', allowedTiers)
  .eq('is_active', true);
```

## Database Migrations

### Migration Strategy
- Use Supabase CLI for migrations
- Keep migrations in version control
- Test migrations on staging before production
- Use reversible migrations when possible

### Example Migration
```sql
-- Migration: Add workspace support to conversations
ALTER TABLE group_conversations
ADD COLUMN workspace_id uuid REFERENCES workspaces(id) ON DELETE SET NULL;

-- Add index for performance
CREATE INDEX idx_group_conversations_workspace_id
ON group_conversations(workspace_id);

-- Update RLS policy
DROP POLICY IF EXISTS "Users can only see their own conversations"
ON group_conversations;

CREATE POLICY "Users can only see their own conversations"
ON group_conversations
FOR ALL
USING (
  auth.uid() = user_id OR
  (workspace_id IS NOT NULL AND EXISTS (
    SELECT 1 FROM workspaces w
    WHERE w.id = workspace_id AND w.user_id = auth.uid()
  ))
);
```

## Performance Considerations

### Indexing Strategy
```sql
-- Conversation queries
CREATE INDEX idx_group_conversations_user_updated
ON group_conversations(user_id, updated_at DESC);

-- Message tree queries
CREATE INDEX idx_messages_conversation_parent
ON messages(conversation_id, parent_message_id);

-- Usage tracking
CREATE INDEX idx_usage_logs_user_date
ON usage_logs(user_id, created_at DESC);
```

### Query Optimization
- Use select() to fetch only needed columns
- Implement pagination for large datasets
- Use database functions for complex operations
- Cache frequently accessed data (models, providers)
