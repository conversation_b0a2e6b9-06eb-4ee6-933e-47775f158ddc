# Error Monitoring Setup

This document describes the enhanced error monitoring configuration for the Sabi Chat application, which uses Sentry for comprehensive error tracking.

## Overview

The application implements error monitoring using Sentry:
- **Sentry**: Primary and sole error monitoring service with enhanced configuration
- **Global Error Handlers**: Comprehensive coverage for unhandled promises and exceptions
- **Error Boundaries**: React component error catching with detailed context
- **Async Error Handling**: Utilities for consistent async operation error handling

The service is configured to:
- Only operate in production environment
- Avoid noise in development
- Provide comprehensive error context and metadata
- Filter out non-actionable errors
- Include user context and session information
- Support source maps for meaningful stack traces

## Environment Variables

Add the following environment variables to your `.env.local` file for development and to your deployment environment for production:

```bash
# Sentry Configuration
# Get your DSN from: Sentry Dashboard -> Settings -> Projects -> [Your Project] -> Client Keys (DSN)
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/1234567
SENTRY_DSN=https://<EMAIL>/1234567

# Organization and project settings
SENTRY_ORG=vertex-d7
SENTRY_PROJECT=sabi-chat

# Auth token for source map uploads (required for production)
# Get from: Sentry Dashboard -> Settings -> Auth Tokens
# Scopes needed: project:releases, project:write, org:read
SENTRY_AUTH_TOKEN=your_sentry_auth_token

# Optional: App version for release tracking
SENTRY_RELEASE=
NEXT_PUBLIC_APP_VERSION=1.0.0

# Environment
NODE_ENV=production
```

**Important Security Notes:**
- Never commit the actual `SENTRY_AUTH_TOKEN` to version control
- Use different DSNs for development and production environments if desired
- The `NEXT_PUBLIC_SENTRY_DSN` is exposed to the browser, so it's safe to be public
- The `SENTRY_DSN` (without NEXT_PUBLIC_) is used for server-side reporting only

## Configuration Files

### Sentry Configuration
- `sentry.client.config.ts` - Client-side Sentry configuration
- `sentry.server.config.ts` - Server-side Sentry configuration
- `sentry.edge.config.ts` - Edge runtime Sentry configuration

## Key Features

### Environment-Based Activation
- **Production**: Sentry is active with full error reporting
- **Development**: Only console logging, no external reporting

### Enhanced Error Reporting Strategy
1. **AppError instances**: Automatically reported via constructor with enhanced context
2. **Unhandled errors**: Captured via `handleError()` utility with proper categorization
3. **React errors**: Captured via Error Boundaries with component stack traces
4. **Global errors**: Captured via Global Error Boundary with critical-level reporting
5. **Async errors**: Handled via `withAsyncErrorHandling` wrapper
6. **Promise rejections**: Global handlers for unhandled promise rejections
7. **Window errors**: Client-side error handlers for uncaught exceptions

### Error Context Enhancement
Sentry receives rich context including:
- Error codes and types with proper categorization
- User information (ID, email, username when available)
- Request context and URL information
- Server/client environment details
- Custom metadata and tags
- Component stack traces for React errors
- Operation context for async operations
- Browser information (user agent, language, platform)
- Session information (start time, referrer)
- Release and environment tracking

### Error Filtering
Intelligent filtering to reduce noise:
- Network errors that are not actionable
- ResizeObserver loop limit exceeded errors
- Script errors without meaningful information
- Database connection timeouts handled gracefully
- Expected authentication errors (JWT expired, invalid tokens)
- Edge runtime specific non-critical errors

## Usage Examples

### Custom Error Reporting
```typescript
import { AppError, handleError } from '@/lib/error';

// Using AppError (automatically reported)
throw new AppError('Something went wrong', 'CUSTOM_ERROR', 500);

// Handling unknown errors
try {
  // risky operation
} catch (error) {
  const appError = handleError(error);
  // Error is now reported to both services
}
```

### Manual Error Reporting
```typescript
import { reportError } from '@/lib/error';

reportError(new Error('Manual error'), {
  tags: { component: 'UserProfile' },
  extra: { userId: '123', action: 'update' },
  context: 'User profile update failed'
});
```

## Error Boundaries

### Global Error Boundary
Located at `src/app/global-error.tsx`, catches unhandled errors in the app router.

### Component Error Boundary
Located at `src/components/ErrorBoundary.tsx`, provides granular error catching for specific components.

## Monitoring Dashboard Access

### Sentry
- Organization: vertex-d7
- Project: sabi-chat
- Dashboard: [Sentry Dashboard URL]

## Best Practices

1. **Use AppError for known error conditions**
2. **Include relevant context in error metadata**
3. **Test error reporting in staging environment**
4. **Monitor error rates and patterns regularly**
5. **Set up alerts for critical error thresholds**

## Production Deployment Checklist

Before deploying to production, ensure the following:

### ✅ Environment Variables
- [ ] `NEXT_PUBLIC_SENTRY_DSN` is set with your client DSN
- [ ] `SENTRY_DSN` is set with your server DSN
- [ ] `SENTRY_AUTH_TOKEN` is set with proper scopes (project:releases, project:write, org:read)
- [ ] `SENTRY_ORG` and `SENTRY_PROJECT` match your Sentry project
- [ ] `NODE_ENV=production` is set
- [ ] `SENTRY_RELEASE` is set for release tracking (optional but recommended)

### ✅ Configuration Verification
- [ ] Sentry configurations use environment variables (no hardcoded values)
- [ ] Sample rates are optimized for production (tracesSampleRate: 0.1 or lower)
- [ ] Session replay rates are reasonable (replaysSessionSampleRate: 0.01)
- [ ] Error filtering is enabled to reduce noise
- [ ] Source maps are configured for upload

### ✅ Security
- [ ] Auth token is not committed to version control
- [ ] DSNs are properly scoped (client vs server)
- [ ] Sensitive data filtering is enabled
- [ ] Tunnel route is configured if needed to bypass ad blockers

## Troubleshooting

### Common Issues

1. **Errors not appearing in dashboards**
   - Verify environment variables are set correctly
   - Check that NODE_ENV is set to 'production'
   - Ensure API keys are valid
   - Check Sentry project quotas and limits

2. **High quota usage**
   - Reduce `tracesSampleRate` (recommended: 0.1 or lower for production)
   - Reduce `replaysSessionSampleRate` (recommended: 0.01 for production)
   - Ensure error filtering is working properly
   - Review and optimize breadcrumb collection

3. **Source maps not uploading**
   - Verify `SENTRY_AUTH_TOKEN` has correct scopes
   - Check that build process has network access to Sentry
   - Ensure `SENTRY_ORG` and `SENTRY_PROJECT` are correct
   - Check build logs for upload errors

4. **"navigator is not defined" error in production**
   - This has been fixed by adding proper browser/server environment checks
   - Client-side configurations only run in browser context
   - Server-side configurations are properly isolated

5. **Missing context in error reports**
   - Verify metadata is being passed correctly
   - Check that user context is being set properly
   - Ensure breadcrumbs are being collected

### Debug Mode

To enable debug logging for error monitoring services, set:
```bash
# In development only
SENTRY_DEBUG=true
```

### Testing Your Setup

1. **Test error reporting:**
   ```javascript
   // In browser console (production)
   throw new Error('Test error for Sentry');
   ```

2. **Test API error tracking:**
   Visit `/api/sentry-example-api` to trigger a server-side error

3. **Verify source maps:**
   Check that stack traces in Sentry show original source code, not minified

4. **Test performance monitoring:**
   Navigate through your app and verify traces appear in Sentry Performance tab

## Performance Considerations

- Error reporting is asynchronous and non-blocking
- Failed error reports are logged to console but don't affect application functionality
- Sentry uses efficient batching and sampling strategies
- Network failures in error reporting are handled gracefully

## Security

- User IP collection is disabled for privacy
- Sensitive data is filtered from error reports
- API keys are properly scoped with minimal required permissions
- Error reports include only necessary debugging information
